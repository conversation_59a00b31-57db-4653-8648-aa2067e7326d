package org.jeecg.modules.comInterface.async;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.modules.comInterface.dto.CompanyRegBatchCreateDTO;
import org.jeecg.modules.comInterface.service.IBatchTaskStatusService;
import org.jeecg.modules.comInterface.service.impl.CompanyRegApiServiceImpl;
import org.jeecg.modules.comInterface.vo.BatchResultVO;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.reg.entity.CompanyTeam;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;

/**
 * @Description: 异步批量处理执行器
 * @Author: system
 * @Date: 2024-12-08
 * @Version: V1.0
 */
@Slf4j
@Component
public class BatchProcessAsyncExecutor {

    @Autowired
    private IBatchTaskStatusService batchTaskStatusService;

    @Autowired
    private CompanyRegApiServiceImpl companyRegApiService;

    @Autowired
    private ObjectMapper objectMapper;

    // 并发控制信号量，限制同时处理的人员数量
    private final Semaphore semaphore = new Semaphore(10);

    /**
     * 异步处理企业批量预约数据
     */
    @Async("batchProcessExecutor")
    public CompletableFuture<String> processBatchCompanyRegAsync(String taskId,
                                                               CompanyRegBatchCreateDTO batchCreateDTO,
                                                               CompanyReg companyReg) {
        log.info("Starting async batch processing for task: {}", taskId);

        try {
            // 更新任务状态为处理中
            batchTaskStatusService.updateProgress(taskId, 0, 0, 0);

            BatchResultVO<CustomerReg> result = new BatchResultVO<>();
            List<CustomerReg> successList = new ArrayList<>();
            List<BatchResultVO.FailureItem<CustomerReg>> failureList = new ArrayList<>();

            CompanyRegBatchCreateDTO.PlanInfoDTO planInfo = batchCreateDTO.getPlanInfo();

            // 计算总人员数量
            int totalPersonnel = calculateTotalPersonnel(planInfo);
            int processedCount = 0;

            // 处理每个分组的人员数据
            if (CollectionUtils.isNotEmpty(planInfo.getTeamsInfo())) {
                for (CompanyRegBatchCreateDTO.TeamsInfoDTO teamInfo : planInfo.getTeamsInfo()) {
                    processedCount += processTeamPersonnelAsync(taskId, teamInfo, companyReg,
                                                              successList, failureList,
                                                              processedCount, totalPersonnel);
                }
            }

            // 设置最终结果
            result.setSuccessList(successList);
            result.setFailureList(failureList);
            result.setSuccessCount(successList.size());
            result.setFailureCount(failureList.size());
            result.setTotal(successList.size() + failureList.size());

            // 序列化结果
            String resultJson = objectMapper.writeValueAsString(result);

            // 完成任务
            batchTaskStatusService.completeTask(taskId, resultJson);

            // 通知企业端
            companyRegApiService.notifyEnterpriseProgress(planInfo.getId(), result);

            log.info("Async batch processing completed for task: {}. Success: {}, Failure: {}",
                    taskId, result.getSuccessCount(), result.getFailureCount());

            return CompletableFuture.completedFuture(taskId);

        } catch (Exception e) {
            log.error("Async batch processing failed for task: {}", taskId, e);
            batchTaskStatusService.failTask(taskId, e.getMessage());
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 计算总人员数量
     */
    private int calculateTotalPersonnel(CompanyRegBatchCreateDTO.PlanInfoDTO planInfo) {
        int total = 0;
        if (CollectionUtils.isNotEmpty(planInfo.getTeamsInfo())) {
            for (CompanyRegBatchCreateDTO.TeamsInfoDTO teamInfo : planInfo.getTeamsInfo()) {
                if (CollectionUtils.isNotEmpty(teamInfo.getExaminedPersonnelInfo())) {
                    total += teamInfo.getExaminedPersonnelInfo().size();
                }
            }
        }
        return total;
    }

    /**
     * 异步处理分组人员数据（带并发控制）
     */
    private int processTeamPersonnelAsync(String taskId,
                                        CompanyRegBatchCreateDTO.TeamsInfoDTO teamInfo,
                                        CompanyReg companyReg,
                                        List<CustomerReg> successList,
                                        List<BatchResultVO.FailureItem<CustomerReg>> failureList,
                                        int currentProcessed,
                                        int totalPersonnel) {

        if (CollectionUtils.isEmpty(teamInfo.getExaminedPersonnelInfo())) {
            return 0;
        }

        // 获取分组信息
        CompanyTeam companyTeam = companyRegApiService.getCompanyTeamById(teamInfo.getId());
        if (companyTeam == null) {
            log.warn("Company team not found: {}", teamInfo.getId());
            return 0;
        }

        int processedInThisTeam = 0;

        for (CompanyRegBatchCreateDTO.ExaminedPersonnelInfoDTO personnelInfo : teamInfo.getExaminedPersonnelInfo()) {
            try {
                // 获取信号量，控制并发
                semaphore.acquire();

                try {
                    // 处理单个人员
                    CustomerReg customerReg = companyRegApiService.processPersonnelInfo(personnelInfo, companyReg, companyTeam);
                    if (customerReg != null) {
                        synchronized (successList) {
                            successList.add(customerReg);
                        }
                    }
                } finally {
                    semaphore.release();
                }

                processedInThisTeam++;

                // 更新进度（每处理10个人员或最后一个时更新）
                if (processedInThisTeam % 10 == 0 ||
                    processedInThisTeam == teamInfo.getExaminedPersonnelInfo().size()) {

                    int totalProcessed = currentProcessed + processedInThisTeam;
                    synchronized (this) {
                        batchTaskStatusService.updateProgress(taskId, totalProcessed,
                                                            successList.size(), failureList.size());
                    }
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Processing interrupted for task: {}", taskId, e);
                break;
            } catch (Exception e) {
                log.error("Failed to process personnel: {}",
                         personnelInfo.getBasicInfo().getActualName(), e);

                // 记录失败
                companyRegApiService.recordFailure(personnelInfo, companyReg, companyTeam, e.getMessage());

                // 添加到失败列表
                BatchResultVO.FailureItem<CustomerReg> failureItem = new BatchResultVO.FailureItem<>();
                failureItem.setData(companyRegApiService.createCustomerRegFromPersonnel(
                                   personnelInfo, companyReg, companyTeam));
                failureItem.setReason(e.getMessage());

                synchronized (failureList) {
                    failureList.add(failureItem);
                }

                processedInThisTeam++;
            }
        }

        return processedInThisTeam;
    }
}
