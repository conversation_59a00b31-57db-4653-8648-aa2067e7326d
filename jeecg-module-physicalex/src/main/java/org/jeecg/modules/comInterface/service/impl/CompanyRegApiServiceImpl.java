package org.jeecg.modules.comInterface.service.impl;

import cn.hutool.core.util.PhoneUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.IdCardUtils;
import org.jeecg.excommons.utils.SequenceGenerator;
import org.jeecg.excommons.utils.UrlUtils;
import org.jeecg.modules.basicinfo.entity.Company;
import org.jeecg.modules.basicinfo.service.ICompanyService;
import org.jeecg.modules.basicinfo.service.ISequencesService;
import org.jeecg.modules.comInterface.dto.*;
import org.jeecg.modules.comInterface.service.ICompanyRegApiService;
import org.jeecg.modules.comInterface.service.IEnterpriseNotificationService;
import org.jeecg.modules.comInterface.vo.BatchResultVO;
import org.jeecg.modules.comInterface.entity.BatchTaskStatus;
import org.jeecg.modules.comInterface.service.IBatchTaskStatusService;
import org.jeecg.modules.comInterface.async.BatchProcessAsyncExecutor;
import org.jeecg.modules.occu.entity.*;
import org.jeecg.modules.occu.service.*;
import org.jeecg.modules.reg.entity.*;
import org.jeecg.modules.reg.mapper.CompanyRegMapper;
import org.jeecg.modules.reg.mapper.CompanyTeamMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.reg.service.*;
import org.jeecg.modules.summary.bo.ReportBean;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * @Description: 企业预约API服务实现
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Slf4j
@Service
public class CompanyRegApiServiceImpl implements ICompanyRegApiService {

    @Autowired
    private ICompanyRegService companyRegService;

    @Autowired
    private ICompanyTeamService companyTeamService;

    @Autowired
    private ICustomerRegService customerRegService;

    @Autowired
    private ICustomerService customerService;

    @Autowired
    private ICompanyService companyService;

    @Autowired
    private CompanyRegMapper companyRegMapper;
    @Autowired
    private CompanyTeamMapper companyTeamMapper;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private SequenceGenerator sequenceGenerator;
    @Autowired
    private ISequencesService sequencesService;
    @Autowired
    private ICompanyImportRecordService companyImportRecordService;

    // 职业病问卷相关服务
    @Autowired
    private IZyInquiryOccuHistoryService zyInquiryOccuHistoryService;
    @Autowired
    private IZyInquiryRadiationHistoryService zyInquiryRadiationHistoryService;
    @Autowired
    private IZyInquiryDiseaseHistoryService zyInquiryDiseaseHistoryService;
    @Autowired
    private IZyInquiryFamilyHistoryService zyInquiryFamilyHistoryService;
    @Autowired
    private IZyInquiryMaritalStatusService zyInquiryMaritalStatusService;
    @Autowired
    private IZyInquirySymptomService zyInquirySymptomService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private IBatchTaskStatusService batchTaskStatusService;

    @Autowired
    private BatchProcessAsyncExecutor batchProcessAsyncExecutor;

    @Autowired
    private IEnterpriseNotificationService enterpriseNotificationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CompanyReg createCompanyReg(CompanyRegCreateDTO createDTO) {
        log.info("Creating company registration: {}", createDTO.getRegName());

        // 创建企业预约
        CompanyReg companyReg = new CompanyReg();
        BeanUtils.copyProperties(createDTO, companyReg);

        // 设置默认值
        companyReg.setCheckoutStatus(0);
        companyReg.setLockStatus(0);
        companyReg.setCreateTime(new Date());

        // 生成团报编号
        companyReg.setCompanyReportNo(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_COMPANY_REPORT_NO));

        // 保存企业预约
        companyRegService.save(companyReg);

        // 创建分组
        if (CollectionUtils.isNotEmpty(createDTO.getTeams())) {
            List<CompanyTeam> teams = new ArrayList<>();
            for (CompanyTeamCreateDTO teamDTO : createDTO.getTeams()) {
                CompanyTeam team = new CompanyTeam();
                BeanUtils.copyProperties(teamDTO, team);
                team.setCompanyRegId(companyReg.getId());
                team.setCreateTime(new Date());
                //team.setDelFlag(0);
                teams.add(team);
            }
            companyTeamService.saveBatch(teams);
        }

        log.info("Company registration created successfully: {}", companyReg.getId());
        return companyReg;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchResultVO<CustomerReg> batchCreateCompanyReg(CompanyRegBatchCreateDTO batchCreateDTO) {
        log.info("Processing batch company registration data from enterprise");

        CompanyRegBatchCreateDTO.PlanInfoDTO planInfo = batchCreateDTO.getPlanInfo();
        if (planInfo == null) {
            throw new RuntimeException("Plan info cannot be null");
        }

        // 验证企业预约是否存在
        CompanyReg companyReg = companyRegMapper.selectById(planInfo.getId());
        if (companyReg == null) {
            throw new RuntimeException("Company registration not found: " + planInfo.getId());
        }

        // 计算总人员数量，如果数量较大则使用异步处理
        int totalPersonnel = calculateTotalPersonnel(planInfo);

        // 如果人员数量超过阈值（比如100人），则使用异步处理
        if (totalPersonnel > 100) {
            log.info("Large batch detected ({} personnel), switching to async processing", totalPersonnel);
            return processLargeBatchAsync(batchCreateDTO, companyReg, totalPersonnel);
        } else {
            // 小批量数据仍然使用同步处理
            log.info("Small batch ({} personnel), using sync processing", totalPersonnel);
            return processSmallBatchSync(batchCreateDTO, companyReg);
        }
    }

    /**
     * 处理大批量数据（异步）
     */
    private BatchResultVO<CustomerReg> processLargeBatchAsync(CompanyRegBatchCreateDTO batchCreateDTO,
                                                            CompanyReg companyReg,
                                                            int totalPersonnel) {
        CompanyRegBatchCreateDTO.PlanInfoDTO planInfo = batchCreateDTO.getPlanInfo();

        // 创建批量任务
        BatchTaskStatus task = batchTaskStatusService.createTask(
                "BATCH_COMPANY_REG",
                planInfo.getId(),
                totalPersonnel
        );

        // 提交异步任务
        batchProcessAsyncExecutor.processBatchCompanyRegAsync(task.getId(), batchCreateDTO, companyReg)
                .whenComplete((taskId, throwable) -> {
                    if (throwable != null) {
                        log.error("Async batch processing failed for task: {}", task.getId(), throwable);
                        batchTaskStatusService.failTask(task.getId(), throwable.getMessage());
                    }
                });

        // 立即返回一个表示异步处理中的结果
        BatchResultVO<CustomerReg> result = new BatchResultVO<>();
        result.setTotal(totalPersonnel);
        result.setSuccessCount(0);
        result.setFailureCount(0);
        result.setSuccessList(new ArrayList<>());
        result.setFailureList(new ArrayList<>());
        result.setMessage("大批量数据已提交异步处理，任务ID: " + task.getId() + "，处理完成后将自动通知企业端");
        result.setTaskId(task.getId()); // 添加任务ID到结果中

        log.info("Large batch submitted for async processing, taskId: {}", task.getId());
        return result;
    }

    /**
     * 处理小批量数据（同步）
     */
    private BatchResultVO<CustomerReg> processSmallBatchSync(CompanyRegBatchCreateDTO batchCreateDTO,
                                                           CompanyReg companyReg) {
        CompanyRegBatchCreateDTO.PlanInfoDTO planInfo = batchCreateDTO.getPlanInfo();

        BatchResultVO<CustomerReg> result = new BatchResultVO<>();
        List<CustomerReg> successList = new ArrayList<>();
        List<BatchResultVO.FailureItem<CustomerReg>> failureList = new ArrayList<>();

        // 处理每个分组的人员数据
        if (CollectionUtils.isNotEmpty(planInfo.getTeamsInfo())) {
            for (CompanyRegBatchCreateDTO.TeamsInfoDTO teamInfo : planInfo.getTeamsInfo()) {
                processTeamPersonnel(teamInfo, companyReg, successList, failureList);
            }
        }

        result.setSuccessList(successList);
        result.setFailureList(failureList);
        result.setSuccessCount(successList.size());
        result.setFailureCount(failureList.size());
        result.setTotal(successList.size() + failureList.size());

        log.info("Small batch processing completed. Success: {}, Failure: {}",
                result.getSuccessCount(), result.getFailureCount());

        // 同步处理完成后也通知企业端
        notifyEnterpriseProgress(planInfo.getId(), result);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchResultVO<CustomerReg> batchCreateCustomerReg(CustomerRegBatchCreateDTO batchCreateDTO) {
        log.info("Batch creating customer registrations, count: {}", batchCreateDTO.getCustomerList().size());

        String companyRegId = batchCreateDTO.getCompanyRegId();

        // 验证企业预约是否存在
        CompanyReg companyReg = companyRegMapper.selectById(companyRegId);
        if (companyReg == null) {
            throw new RuntimeException("Company registration not found: " + companyRegId);
        }

        // 获取分组列表
        List<CompanyTeam> companyTeams = companyTeamMapper.selectByMainId(companyReg.getId());
        if (CollectionUtils.isEmpty(companyTeams)) {
            throw new RuntimeException("No teams found for company registration: " + companyRegId);
        }

        BatchResultVO<CustomerReg> result = new BatchResultVO<>();
        List<CustomerReg> successList = new ArrayList<>();
        List<BatchResultVO.FailureItem<CustomerReg>> failureList = new ArrayList<>();

        for (CustomerRegCreateDTO customerDTO : batchCreateDTO.getCustomerList()) {
            try {
                CustomerReg customerReg = processCustomerReg(customerDTO, companyReg, companyTeams);
                if (customerReg != null) {
                    successList.add(customerReg);
                }
            } catch (Exception e) {
                log.error("Failed to create customer registration for: {}", customerDTO.getName(), e);
                CustomerReg failedCustomer = new CustomerReg();
                BeanUtils.copyProperties(customerDTO, failedCustomer);
                failureList.add(new BatchResultVO.FailureItem<>(failedCustomer, e.getMessage()));
            }
        }

        result.setTotal(batchCreateDTO.getCustomerList().size());
        result.setSuccessCount(successList.size());
        result.setFailureCount(failureList.size());
        result.setSuccessList(successList);
        result.setFailureList(failureList);

        log.info("Batch creation completed. Success: {}, Failure: {}", successList.size(), failureList.size());
        return result;
    }

    @Override
    public CompanyReg getCompanyRegById(String companyRegId) {
        return companyRegMapper.selectById(companyRegId);
    }

    @Override
    public BatchResultVO<CustomerReg> getCustomerRegList(String companyRegId, Integer pageNo, Integer pageSize) {
        Page<CustomerReg> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<CustomerReg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerReg::getCompanyRegId, companyRegId);
        queryWrapper.eq(CustomerReg::getDelFlag, 0);
        queryWrapper.orderByDesc(CustomerReg::getCreateTime);

        Page<CustomerReg> resultPage = customerRegMapper.selectPage(page, queryWrapper);

        BatchResultVO<CustomerReg> result = new BatchResultVO<>();
        result.setTotal((int) resultPage.getTotal());
        result.setSuccessCount((int) resultPage.getTotal());
        result.setFailureCount(0);
        result.setSuccessList(resultPage.getRecords());
        result.setFailureList(new ArrayList<>());

        return result;
    }

    /**
     * 处理单个客户登记
     */
    private CustomerReg processCustomerReg(CustomerRegCreateDTO customerDTO, CompanyReg companyReg, List<CompanyTeam> companyTeams) {
        // 创建CustomerReg对象
        CustomerReg customerReg = new CustomerReg();
        BeanUtils.copyProperties(customerDTO, customerReg);

        // 设置企业信息
        customerReg.setCompanyRegId(companyReg.getId());
        customerReg.setCompanyRegName(companyReg.getRegName());
        customerReg.setCompanyId(companyReg.getCompanyId());
        customerReg.setCompanyName(companyReg.getCompanyName());

        // 处理姓名（去除空格）
        String name = customerReg.getName();
        customerReg.setName(name.replaceAll("\\s", ""));

        // 验证数据
        validateCustomerReg(customerReg);

        // 检查是否已存在
        boolean updateFlag = false;
        List<CustomerReg> existingCustomers = customerRegService.list(
                new LambdaQueryWrapper<CustomerReg>()
                        .eq(CustomerReg::getIdCard, customerReg.getIdCard())
                        .eq(CustomerReg::getCompanyRegId, companyReg.getId())
                        .orderByDesc(CustomerReg::getExamNo)
        );

        if (CollectionUtils.isNotEmpty(existingCustomers)) {
            CustomerReg existing = existingCustomers.get(0);
            if (StringUtils.equals(existing.getStatus(), "未登记")) {
                updateFlag = true;
                customerReg.setId(existing.getId());
                customerReg.setExamNo(existing.getExamNo());
            } else {
                throw new RuntimeException("Customer with ID card already registered: " + customerReg.getIdCard());
            }
        } else {
            // 生成体检号
            customerReg.setExamNo(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_EXAMNO));
        }

        // 处理身份证信息
        processIdCard(customerReg);

        // 匹配分组
        CompanyTeam matchedTeam = matchTeam(customerReg, companyTeams);
        if (matchedTeam == null) {
            throw new RuntimeException("No suitable team found for customer: " + customerReg.getName());
        }

        // 设置分组信息
        customerReg.setTeamId(matchedTeam.getId());
        customerReg.setTeamNum(matchedTeam.getTeamNum());
        customerReg.setTeamName(matchedTeam.getName());
        customerReg.setRiskFactor(StringUtils.isNotBlank(customerReg.getRiskFactor()) ?
                customerReg.getRiskFactor() : matchedTeam.getRisks());
        customerReg.setJobStatus(StringUtils.isNotBlank(customerReg.getJobStatus()) ?
                customerReg.getJobStatus() : matchedTeam.getPost());

        // 处理部门信息
        processDepartment(customerReg);

        // 设置其他默认值
        setDefaultValues(customerReg);

        // 处理工龄信息
        processWorkYears(customerReg);

        // 创建或更新Customer
        Customer customer = saveCustomerWithRateLimit(customerReg);
        customerReg.setCustomerId(customer.getId());
        customerReg.setArchivesNum(customer.getArchivesNum());

        // 保存CustomerReg
        if (updateFlag) {
            customerRegService.updateById(customerReg);
        } else {
            customerRegService.save(customerReg);
        }

        log.info("Customer registration processed: {}", customerReg.getName());
        return customerReg;
    }

    /**
     * 验证客户登记数据
     */
    private void validateCustomerReg(CustomerReg customerReg) {
        if (StringUtils.isBlank(customerReg.getName())) {
            throw new RuntimeException("Name cannot be empty");
        }
        if (StringUtils.isBlank(customerReg.getGender())) {
            throw new RuntimeException("Gender cannot be empty");
        }
        if (customerReg.getBirthday() == null) {
            throw new RuntimeException("Birthday cannot be empty");
        }
        if (!PhoneUtil.isPhone(customerReg.getPhone())) {
            throw new RuntimeException("Invalid phone number: " + customerReg.getPhone());
        }
    }

    /**
     * 处理身份证信息
     */
    private void processIdCard(CustomerReg customerReg) {
        String idCard = customerReg.getIdCard();
        String idCardType = customerReg.getCardType();
        idCardType = StringUtils.isBlank(idCardType) ? "居民身份证" : idCardType;
        customerReg.setCardType(idCardType);

        if (StringUtils.equals(idCardType, "居民身份证")) {
            if (StringUtils.isNotBlank(idCard)) {
                if (!IdCardUtils.isValidCard(idCard)) {
                    throw new RuntimeException("Invalid ID card number: " + idCard);
                } else {
                    customerReg.setBirthday(IdCardUtils.getBirthDate(idCard));
                    customerReg.setAge(IdCardUtils.getAge(idCard));
                    customerReg.setGender(IdCardUtils.getGender(idCard));
                }
            }
        }
    }

    /**
     * 匹配分组
     */
    private CompanyTeam matchTeam(CustomerReg customerReg, List<CompanyTeam> companyTeams) {
        if (StringUtils.isNotBlank(customerReg.getTeamName())) {
            // 根据分组名称匹配
            return companyTeams.stream()
                    .filter(team -> StringUtils.equals(team.getName(), customerReg.getTeamName()))
                    .findFirst()
                    .orElse(null);
        } else {
            // 根据条件自动匹配
            return companyTeams.stream()
                    .filter(team -> team.contains(customerReg.getAge(), customerReg.getGender(), customerReg.getMarriageStatus()))
                    .findFirst()
                    .orElse(null);
        }
    }

    /**
     * 处理部门信息
     */
    private void processDepartment(CustomerReg customerReg) {
        if (StringUtils.isNotBlank(customerReg.getCompanyDeptId())) {
            Company dept = companyService.getById(customerReg.getCompanyDeptId());
            if (Objects.nonNull(dept)) {
                customerReg.setCompanyDeptName(dept.getName());
            }
        }
        if (StringUtils.isNotBlank(customerReg.getCompanyDeptName())) {
            Company dept = companyService.getOne(
                    new LambdaQueryWrapper<Company>()
                            .eq(Company::getName, customerReg.getCompanyDeptName())
                            .eq(Company::getPid, customerReg.getCompanyId())
                            .last("limit 1")
            );
            if (Objects.nonNull(dept)) {
                customerReg.setCompanyDeptId(dept.getId());
            }
        }
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(CustomerReg customerReg) {
        customerReg.setAgeUnit("岁");
        customerReg.setCustomerCategory("企业客户");
        customerReg.setCreateTime(new Date());
        customerReg.setStatus(ExConstants.REG_STATUS_WAIT);
        customerReg.setPaymentState(ExConstants.PAY_STATUS_WAIT);
        customerReg.setDelFlag("0");
        customerReg.setRetrieveStatus("0");

        Date appointmentDate = customerReg.getAppointmentDate();
        if (appointmentDate == null) {
            appointmentDate = new Date();
            customerReg.setAppointmentDate(appointmentDate);
        }
        if (customerReg.getAppointmentSort() == null) {
            LocalDate localDate = appointmentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            customerReg.setAppointmentSort(sequenceGenerator.getSerialNoBaseDate(ExConstants.SEQ_APPOINTMENT_NO, localDate));
        }
    }

    /**
     * 处理工龄信息
     */
    private void processWorkYears(CustomerReg customerReg) {
        if (StringUtils.isNotBlank(customerReg.getWorkYears()) && StringUtils.isBlank(customerReg.getWorkMonths())) {
            String[] workYearAndMonth = StringUtils.split(customerReg.getWorkYears(), ".");
            if (workYearAndMonth.length == 2) {
                customerReg.setWorkYears(workYearAndMonth[0]);
                customerReg.setWorkMonths(workYearAndMonth[1]);
            } else {
                customerReg.setWorkYears(workYearAndMonth[0]);
                customerReg.setWorkMonths("0");
            }
        }

        if (StringUtils.isNotBlank(customerReg.getRiskYears()) && StringUtils.isBlank(customerReg.getRiskMonths())) {
            String[] riskYearAndMonth = StringUtils.split(customerReg.getRiskYears(), ".");
            if (riskYearAndMonth.length == 2) {
                customerReg.setRiskYears(riskYearAndMonth[0]);
                customerReg.setRiskMonths(riskYearAndMonth[1]);
            } else {
                customerReg.setRiskYears(riskYearAndMonth[0]);
                customerReg.setRiskMonths("0");
            }
        }
    }

    /**
     * 创建Customer（限流版本）
     */
    private Customer saveCustomerWithRateLimit(CustomerReg customerReg) {
        // 这里应该调用现有的customerService方法
        // 为了简化，直接创建一个基本的Customer对象
        Customer customer = new Customer();
        customer.setName(customerReg.getName());
        customer.setGender(customerReg.getGender());
        customer.setIdCard(customerReg.getIdCard());
        customer.setCardType(customerReg.getCardType());
        customer.setBirthday(customerReg.getBirthday());
        customer.setAge(customerReg.getAge());
        customer.setPhone(customerReg.getPhone());
        customer.setCreateTime(new Date());
        // customer.setDelFlag(0);

        // 生成档案号
        customer.setArchivesNum(sequencesService.getNextSequenceWithPrefix(ExConstants.SEQ_ARCHIVE_NO));

        customerService.save(customer);
        return customer;
    }

    /**
     * 处理分组人员数据
     */
    private void processTeamPersonnel(CompanyRegBatchCreateDTO.TeamsInfoDTO teamInfo,
                                    CompanyReg companyReg,
                                    List<CustomerReg> successList,
                                    List<BatchResultVO.FailureItem<CustomerReg>> failureList) {

        // 验证分组是否存在
        CompanyTeam companyTeam = companyTeamMapper.selectById(teamInfo.getId());
        if (companyTeam == null) {
            log.warn("Company team not found: {}", teamInfo.getId());
            return;
        }

        if (CollectionUtils.isNotEmpty(teamInfo.getExaminedPersonnelInfo())) {
            for (CompanyRegBatchCreateDTO.ExaminedPersonnelInfoDTO personnelInfo : teamInfo.getExaminedPersonnelInfo()) {
                try {
                    CustomerReg customerReg = processPersonnelInfo(personnelInfo, companyReg, companyTeam);
                    if (customerReg != null) {
                        successList.add(customerReg);
                    }
                } catch (Exception e) {
                    log.error("Failed to process personnel: {}",
                            personnelInfo.getBasicInfo().getActualName(), e);

                    // 记录失败信息到CompanyImportRecord
                    recordFailure(personnelInfo, companyReg, companyTeam, e.getMessage());

                    // 添加到失败列表
                    BatchResultVO.FailureItem<CustomerReg> failureItem = new BatchResultVO.FailureItem<>();
                    failureItem.setData(createCustomerRegFromPersonnel(personnelInfo, companyReg, companyTeam));
                    failureItem.setReason(e.getMessage());
                    failureList.add(failureItem);
                }
            }
        }
    }

    /**
     * 处理单个人员信息
     */
    public CustomerReg processPersonnelInfo(CompanyRegBatchCreateDTO.ExaminedPersonnelInfoDTO personnelInfo,
                                           CompanyReg companyReg,
                                           CompanyTeam companyTeam) throws Exception {

        CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo = personnelInfo.getBasicInfo();
        if (basicInfo == null) {
            throw new RuntimeException("Basic info cannot be null");
        }

        // 验证必填字段
        validateBasicInfo(basicInfo);

        // 创建或更新Customer
        Customer customer = createOrUpdateCustomer(basicInfo);

        // 创建或更新CustomerReg
        CustomerReg customerReg = createOrUpdateCustomerReg(basicInfo, customer, companyReg, companyTeam);

        // 处理职业病问卷
        if (personnelInfo.getQuestionnaireInfo() != null) {
            processQuestionnaire(customerReg.getId(), personnelInfo.getQuestionnaireInfo(), basicInfo);
        }

        return customerReg;
    }

    /**
     * 验证基本信息
     */
    private void validateBasicInfo(CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo) throws Exception {
        if (StringUtils.isBlank(basicInfo.getActualName())) {
            throw new RuntimeException("姓名不能为空");
        }

        if (StringUtils.isBlank(basicInfo.getIdCard())) {
            throw new RuntimeException("身份证号不能为空");
        }

        // 验证身份证号格式
        if (!IdCardUtils.isValidCard(basicInfo.getIdCard())) {
            throw new RuntimeException("身份证号格式不正确");
        }

        // 验证手机号
        if (StringUtils.isNotBlank(basicInfo.getPhone()) && !PhoneUtil.isPhone(basicInfo.getPhone())) {
            throw new RuntimeException("手机号格式不正确");
        }
    }

    /**
     * 创建或更新Customer
     */
    private Customer createOrUpdateCustomer(CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo) {
        // 根据身份证号查找现有客户
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Customer::getIdCard, basicInfo.getIdCard());
        Customer existingCustomer = customerService.getOne(queryWrapper);

        Customer customer;
        if (existingCustomer != null) {
            // 更新现有客户
            customer = existingCustomer;
            updateCustomerFromBasicInfo(customer, basicInfo);
            customerService.updateById(customer);
        } else {
            // 创建新客户
            customer = createCustomerFromBasicInfo(basicInfo);
            customerService.save(customer);
        }

        return customer;
    }

    /**
     * 从基本信息创建Customer
     */
    private Customer createCustomerFromBasicInfo(CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo) {
        Customer customer = new Customer();

        customer.setName(basicInfo.getActualName());
        customer.setIdCard(basicInfo.getIdCard());
        customer.setPhone(basicInfo.getPhone());
        customer.setEmail(basicInfo.getEmail());

        // 处理头像：如果是base64字符串，上传到MinIO
        String avatarPath = processAvatar(basicInfo.getAvatar());
        customer.setAvatar(avatarPath);
        customer.setAddress(basicInfo.getAddress());
        customer.setNation(basicInfo.getNation());
        customer.setProvinceCode(basicInfo.getProvinceCode());
        customer.setProvince(basicInfo.getProvinceName());
        customer.setCityCode(basicInfo.getCityCode());
        customer.setCity(basicInfo.getCityName());
        customer.setAreaCode(basicInfo.getDistrictCode());
        customer.setArea(basicInfo.getDistrictName());
        //customer.setDe(basicInfo.getRemark());

        // 从身份证号解析信息
        if (StringUtils.isNotBlank(basicInfo.getIdCard()) && IdCardUtils.isValidCard(basicInfo.getIdCard())) {
            customer.setBirthday(IdCardUtils.getBirthDate(basicInfo.getIdCard()));
            customer.setAge(IdCardUtils.getAge(basicInfo.getIdCard()));
            customer.setGender(IdCardUtils.getGender(basicInfo.getIdCard()));
        }

        // 设置默认值
        customer.setCreateTime(new Date());
        customer.setArchivesNum(sequencesService.getNextSequenceWithPrefix(ExConstants.SEQ_ARCHIVE_NO));

        return customer;
    }

    /**
     * 更新Customer信息
     */
    private void updateCustomerFromBasicInfo(Customer customer, CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo) {
        customer.setName(basicInfo.getActualName());
        if (StringUtils.isNotBlank(basicInfo.getPhone())) {
            customer.setPhone(basicInfo.getPhone());
        }
        if (StringUtils.isNotBlank(basicInfo.getEmail())) {
            customer.setEmail(basicInfo.getEmail());
        }
        if (StringUtils.isNotBlank(basicInfo.getAvatar())) {
            // 处理头像：如果是base64字符串，上传到MinIO
            String avatarPath = processAvatar(basicInfo.getAvatar());
            customer.setAvatar(avatarPath);
        }
        if (StringUtils.isNotBlank(basicInfo.getAddress())) {
            customer.setAddress(basicInfo.getAddress());
        }
        if (StringUtils.isNotBlank(basicInfo.getNation())) {
            customer.setNation(basicInfo.getNation());
        }
        /*if (StringUtils.isNotBlank(basicInfo.getRemark())) {
            customer.setRemark(basicInfo.getRemark());
        }*/

        customer.setUpdateTime(new Date());
    }

    /**
     * 创建或更新CustomerReg
     */
    private CustomerReg createOrUpdateCustomerReg(CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo,
                                                Customer customer,
                                                CompanyReg companyReg,
                                                CompanyTeam companyTeam) {

        // 查找现有的CustomerReg记录
        LambdaQueryWrapper<CustomerReg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerReg::getIdCard, basicInfo.getIdCard())
                   .eq(CustomerReg::getCompanyRegId, companyReg.getId())
                   .orderByDesc(CustomerReg::getExamNo);
        List<CustomerReg> customerRegs = customerRegService.list(queryWrapper);

        CustomerReg customerReg;
        boolean isUpdate = false;

        if (CollectionUtils.isNotEmpty(customerRegs)) {
            if (StringUtils.equals(customerRegs.get(0).getStatus(), "未登记")) {
                // 更新现有记录
                customerReg = customerRegs.get(0);
                isUpdate = true;
            } else {
                throw new RuntimeException("关联身份证号已登记，不可重复录入！");
            }
        } else {
            // 创建新记录
            customerReg = new CustomerReg();
            customerReg.setExamNo(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_EXAMNO));
        }

        // 设置基本信息
        customerReg.setName(basicInfo.getActualName());
        customerReg.setIdCard(basicInfo.getIdCard());
        customerReg.setPhone(basicInfo.getPhone());
        customerReg.setCustomerId(customer.getId());
        customerReg.setArchivesNum(customer.getArchivesNum());

        // 设置企业和分组信息
        customerReg.setCompanyRegId(companyReg.getId());
        customerReg.setCompanyRegName(companyReg.getRegName());
        customerReg.setCompanyId(companyReg.getCompanyId());
        customerReg.setCompanyName(companyReg.getCompanyName());
        customerReg.setTeamId(companyTeam.getId());
        customerReg.setTeamNum(companyTeam.getTeamNum());
        customerReg.setTeamName(companyTeam.getName());

        // 从身份证号解析信息
        if (StringUtils.isNotBlank(basicInfo.getIdCard()) && IdCardUtils.isValidCard(basicInfo.getIdCard())) {
            customerReg.setBirthday(IdCardUtils.getBirthDate(basicInfo.getIdCard()));
            customerReg.setAge(IdCardUtils.getAge(basicInfo.getIdCard()));
            customerReg.setGender(IdCardUtils.getGender(basicInfo.getIdCard()));
        }

        // 设置默认值（参考importExcel逻辑）
        customerReg.setAgeUnit("岁");
        customerReg.setCustomerCategory("企业客户");
        customerReg.setExamCategory(companyTeam.getExamCategory());
        customerReg.setStatus(ExConstants.REG_STATUS_WAIT);
        customerReg.setPaymentState(ExConstants.PAY_STATUS_WAIT);
        customerReg.setDelFlag("0");
        customerReg.setRetrieveStatus("0");

        // 设置预约信息
        Date appointmentDate = new Date();
        customerReg.setAppointmentDate(appointmentDate);
        LocalDate localDate = appointmentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        customerReg.setAppointmentSort(sequenceGenerator.getSerialNoBaseDate(ExConstants.SEQ_APPOINTMENT_NO, localDate));

        if (isUpdate) {
            customerRegService.updateById(customerReg);
        } else {
            customerReg.setCreateTime(new Date());
            customerRegService.save(customerReg);
        }

        return customerReg;
    }

    /**
     * 处理职业病问卷
     */
    private void processQuestionnaire(String customerRegId, CompanyRegBatchCreateDTO.QuestionnaireInfoDTO questionnaireInfo,
                                    CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo) {
        try {
            // 处理职业史
            if (CollectionUtils.isNotEmpty(questionnaireInfo.getOccupationalHistory())) {
                processOccupationalHistory(customerRegId, questionnaireInfo.getOccupationalHistory(), basicInfo);
            }

            // 处理放射史
            if (CollectionUtils.isNotEmpty(questionnaireInfo.getRadiationHistory())) {
                processRadiationHistory(customerRegId, questionnaireInfo.getRadiationHistory(), basicInfo);
            }

            // 处理疾病史
            if (CollectionUtils.isNotEmpty(questionnaireInfo.getDiseaseHistory())) {
                processDiseaseHistory(customerRegId, questionnaireInfo.getDiseaseHistory(), basicInfo);
            }

            // 处理家族史
            if (CollectionUtils.isNotEmpty(questionnaireInfo.getFamilyHistory())) {
                processFamilyHistory(customerRegId, questionnaireInfo.getFamilyHistory(), basicInfo);
            }

            // 处理婚姻史
            if (CollectionUtils.isNotEmpty(questionnaireInfo.getMaritalHistory())) {
                processMaritalHistory(customerRegId, questionnaireInfo.getMaritalHistory(), basicInfo);
            }

            // 处理症状
            if (CollectionUtils.isNotEmpty(questionnaireInfo.getSymptoms())) {
                processSymptoms(customerRegId, questionnaireInfo.getSymptoms(), basicInfo);
            }

        } catch (Exception e) {
            log.error("Failed to process questionnaire for customerRegId: {}", customerRegId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 处理职业史
     */
    private void processOccupationalHistory(String customerRegId, List<Object> occupationalHistoryList,
                                          CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo) {
        try {
            for (Object item : occupationalHistoryList) {
                if (item == null) continue;

                ZyInquiryOccuHistory occuHistory = objectMapper.convertValue(item, ZyInquiryOccuHistory.class);
                occuHistory.setInquiryId(customerRegId);
                occuHistory.setCreateTime(new Date());
                zyInquiryOccuHistoryService.save(occuHistory);
            }
        } catch (Exception e) {
            log.error("Failed to process occupational history", e);
        }
    }

    /**
     * 处理放射史
     */
    private void processRadiationHistory(String customerRegId, List<Object> radiationHistoryList,
                                       CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo) {
        try {
            for (Object item : radiationHistoryList) {
                if (item == null) continue;

                ZyInquiryRadiationHistory radiationHistory = objectMapper.convertValue(item, ZyInquiryRadiationHistory.class);
                radiationHistory.setInquiryId(customerRegId);
                radiationHistory.setCreateTime(new Date());
                zyInquiryRadiationHistoryService.save(radiationHistory);
            }
        } catch (Exception e) {
            log.error("Failed to process radiation history", e);
        }
    }

    /**
     * 处理疾病史
     */
    private void processDiseaseHistory(String customerRegId, List<Object> diseaseHistoryList,
                                     CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo) {
        try {
            for (Object item : diseaseHistoryList) {
                if (item == null) continue;

                ZyInquiryDiseaseHistory diseaseHistory = objectMapper.convertValue(item, ZyInquiryDiseaseHistory.class);
                diseaseHistory.setInquiryId(customerRegId);
                diseaseHistory.setCreateTime(new Date());
                zyInquiryDiseaseHistoryService.save(diseaseHistory);
            }
        } catch (Exception e) {
            log.error("Failed to process disease history", e);
        }
    }

    /**
     * 处理家族史
     */
    private void processFamilyHistory(String customerRegId, List<Object> familyHistoryList,
                                    CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo) {
        try {
            for (Object item : familyHistoryList) {
                if (item == null) continue;

                ZyInquiryFamilyHistory familyHistory = objectMapper.convertValue(item, ZyInquiryFamilyHistory.class);
                familyHistory.setInquiryId(customerRegId);
                familyHistory.setCreateTime(new Date());
                zyInquiryFamilyHistoryService.save(familyHistory);
            }
        } catch (Exception e) {
            log.error("Failed to process family history", e);
        }
    }

    /**
     * 处理婚姻史
     */
    private void processMaritalHistory(String customerRegId, List<Object> maritalHistoryList,
                                     CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo) {
        try {
            for (Object item : maritalHistoryList) {
                if (item == null) continue;

                ZyInquiryMaritalStatus maritalStatus = objectMapper.convertValue(item, ZyInquiryMaritalStatus.class);
                maritalStatus.setInquiryId(customerRegId);
                maritalStatus.setCreateTime(new Date());
                zyInquiryMaritalStatusService.save(maritalStatus);
            }
        } catch (Exception e) {
            log.error("Failed to process marital history", e);
        }
    }

    /**
     * 处理症状
     */
    private void processSymptoms(String customerRegId, List<Object> symptomsList,
                               CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo) {
        try {
            for (Object item : symptomsList) {
                if (item == null) continue;

                ZyInquirySymptom symptom = objectMapper.convertValue(item, ZyInquirySymptom.class);
                symptom.setInquiryId(customerRegId);
                symptom.setCheckDate(new Date());
                zyInquirySymptomService.save(symptom);
            }
        } catch (Exception e) {
            log.error("Failed to process symptoms", e);
        }
    }

    /**
     * 记录失败信息到CompanyImportRecord
     */
    public void recordFailure(CompanyRegBatchCreateDTO.ExaminedPersonnelInfoDTO personnelInfo,
                             CompanyReg companyReg,
                             CompanyTeam companyTeam,
                             String errorMessage) {
        try {
            CompanyImportRecord record = new CompanyImportRecord();
            record.setCompanyRegId(companyReg.getId());
            record.setCompanyTeamId(companyTeam.getId());
            record.setCompanyRegName(companyReg.getRegName());
            record.setCompanyTeamName(companyTeam.getName());
            record.setName(personnelInfo.getBasicInfo().getActualName());
            record.setIdCard(personnelInfo.getBasicInfo().getIdCard());
            record.setErrMsg(errorMessage);
            record.setCreateTime(new Date());

            companyImportRecordService.save(record);
        } catch (Exception e) {
            log.error("Failed to record failure", e);
        }
    }

    /**
     * 从人员信息创建CustomerReg（用于失败记录）
     */
    public CustomerReg createCustomerRegFromPersonnel(CompanyRegBatchCreateDTO.ExaminedPersonnelInfoDTO personnelInfo,
                                                     CompanyReg companyReg,
                                                     CompanyTeam companyTeam) {
        CustomerReg customerReg = new CustomerReg();
        CompanyRegBatchCreateDTO.BasicInfoDTO basicInfo = personnelInfo.getBasicInfo();

        if (basicInfo != null) {
            customerReg.setName(basicInfo.getActualName());
            customerReg.setIdCard(basicInfo.getIdCard());
            customerReg.setPhone(basicInfo.getPhone());
        }

        customerReg.setCompanyRegId(companyReg.getId());
        customerReg.setCompanyRegName(companyReg.getRegName());
        customerReg.setTeamId(companyTeam.getId());
        customerReg.setTeamName(companyTeam.getName());

        return customerReg;
    }

    /**
     * 处理头像上传
     */
    private String processAvatar(String avatar) {
        if (StringUtils.isBlank(avatar)) {
            return null;
        }

        try {
            // 判断是否为base64字符串
            if (avatar.startsWith("data:image") || avatar.matches("^[A-Za-z0-9+/]*={0,2}$")) {
                // 上传base64图片到MinIO
                return MinioUtil.uploadBase64Image(avatar);
            } else {
                // 如果不是base64，直接返回原值（可能是已有的路径）
                return avatar;
            }
        } catch (Exception e) {
            log.error("Failed to process avatar: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 通知企业端进度
     */
    public void notifyEnterpriseProgress(String companyRegId, BatchResultVO<CustomerReg> result) {
        try {
            log.info("Notifying enterprise about progress for companyRegId: {}, success: {}, failure: {}",
                    companyRegId, result.getSuccessCount(), result.getFailureCount());

            // 使用企业端通知服务发送通知
            enterpriseNotificationService.notifyBatchProgress(companyRegId, result, result.getTaskId());

        } catch (Exception e) {
            log.error("Failed to notify enterprise progress", e);
        }
    }

    @Override
    public String batchCreateCompanyRegAsync(CompanyRegBatchCreateDTO batchCreateDTO) {
        log.info("Starting async batch company registration processing");

        CompanyRegBatchCreateDTO.PlanInfoDTO planInfo = batchCreateDTO.getPlanInfo();
        if (planInfo == null) {
            throw new RuntimeException("Plan info cannot be null");
        }

        // 验证企业预约是否存在
        CompanyReg companyReg = companyRegMapper.selectById(planInfo.getId());
        if (companyReg == null) {
            throw new RuntimeException("Company registration not found: " + planInfo.getId());
        }

        // 计算总人员数量
        int totalPersonnel = calculateTotalPersonnel(planInfo);
        if (totalPersonnel == 0) {
            throw new RuntimeException("No personnel data found to process");
        }

        // 创建批量任务
        BatchTaskStatus task = batchTaskStatusService.createTask(
                "BATCH_COMPANY_REG",
                planInfo.getId(),
                totalPersonnel
        );

        // 提交异步任务
        batchProcessAsyncExecutor.processBatchCompanyRegAsync(task.getId(), batchCreateDTO, companyReg)
                .whenComplete((taskId, throwable) -> {
                    if (throwable != null) {
                        log.error("Async batch processing failed for task: {}", task.getId(), throwable);
                        batchTaskStatusService.failTask(task.getId(), throwable.getMessage());
                    }
                });

        log.info("Async batch task created: {}", task.getId());
        return task.getId();
    }

    @Override
    public BatchTaskStatus getBatchTaskStatus(String taskId) {
        return batchTaskStatusService.getTaskStatus(taskId);
    }

    @Override
    public BatchResultVO<CustomerReg> getBatchTaskResult(String taskId) {
        BatchTaskStatus task = batchTaskStatusService.getTaskStatus(taskId);
        if (task == null) {
            throw new RuntimeException("Task not found: " + taskId);
        }

        if (!"COMPLETED".equals(task.getStatus())) {
            return null; // 任务未完成
        }

        try {
            if (StringUtils.isNotBlank(task.getResultDetail())) {
                return objectMapper.readValue(task.getResultDetail(),
                        objectMapper.getTypeFactory().constructParametricType(BatchResultVO.class, CustomerReg.class));
            }
        } catch (Exception e) {
            log.error("Failed to parse task result for task: {}", taskId, e);
        }

        return null;
    }

    /**
     * 计算总人员数量
     */
    private int calculateTotalPersonnel(CompanyRegBatchCreateDTO.PlanInfoDTO planInfo) {
        int total = 0;
        if (CollectionUtils.isNotEmpty(planInfo.getTeamsInfo())) {
            for (CompanyRegBatchCreateDTO.TeamsInfoDTO teamInfo : planInfo.getTeamsInfo()) {
                if (CollectionUtils.isNotEmpty(teamInfo.getExaminedPersonnelInfo())) {
                    total += teamInfo.getExaminedPersonnelInfo().size();
                }
            }
        }
        return total;
    }

    /**
     * 根据ID获取CompanyTeam（为异步执行器提供访问方法）
     */
    public CompanyTeam getCompanyTeamById(String teamId) {
        return companyTeamMapper.selectById(teamId);
    }
}
