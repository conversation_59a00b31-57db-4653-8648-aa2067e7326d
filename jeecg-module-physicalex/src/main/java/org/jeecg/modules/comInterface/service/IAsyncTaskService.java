package org.jeecg.modules.comInterface.service;

import org.jeecg.modules.comInterface.dto.CompanyRegBatchCreateDTO;
import org.jeecg.modules.reg.entity.CompanyReg;

import java.util.concurrent.CompletableFuture;

/**
 * @Description: 异步任务服务接口
 * @Author: system
 * @Date: 2024-12-08
 * @Version: V1.0
 */
public interface IAsyncTaskService {

    /**
     * 异步处理企业批量预约数据
     */
    CompletableFuture<String> processBatchCompanyRegAsync(String taskId,
                                                        CompanyRegBatchCreateDTO batchCreateDTO,
                                                        CompanyReg companyReg);
}
