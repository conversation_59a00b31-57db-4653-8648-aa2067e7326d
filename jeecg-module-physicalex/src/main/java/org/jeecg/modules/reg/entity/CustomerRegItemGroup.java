package org.jeecg.modules.reg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.ItemInfo;
import org.jeecg.modules.basicinfo.entity.ItemGroupRelation;
import org.jeecg.modules.reg.dto.DependentItemResultDTO;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.summary.bo.TextBean;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: customer_reg_item_group
 * @Author: jeecg-boot
 * @Date: 2024-04-03
 * @Version: V1.0
 */
@Data
@TableName("customer_reg_item_group")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "customer_reg_item_group对象", description = "customer_reg_item_group")
public class CustomerRegItemGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
    /**
     * 登记ID
     */
    @Excel(name = "登记ID", width = 15)
    @ApiModelProperty(value = "登记ID")
    private java.lang.String customerRegId;
    /**
     * 体检号
     */
    @Excel(name = "体检号", width = 15)
    @ApiModelProperty(value = "体检号")
    private java.lang.String examNo;
    /**
     * 组合ID
     */
    @Excel(name = "组合ID", width = 15)
    @ApiModelProperty(value = "组合ID")
    private java.lang.String itemGroupId;
    /**
     * 组合名称
     */
    @Excel(name = "组合名称", width = 15)
    @ApiModelProperty(value = "组合名称")
    private java.lang.String itemGroupName;
    /**
     * 科室ID
     */
    @Excel(name = "科室ID", width = 15)
    @ApiModelProperty(value = "科室ID")
    private java.lang.String departmentId;
    /**
     * 科室名称
     */
    @Excel(name = "科室名称", width = 15)
    @ApiModelProperty(value = "科室名称")
    private java.lang.String departmentName;
    /**
     * 科室项目
     */
    @Excel(name = "科室代码", width = 15)
    @ApiModelProperty(value = "科室代码")
    private java.lang.String departmentCode;
    /**
     * 组合类型
     */
    @Excel(name = "组合类型", width = 15)
    @ApiModelProperty(value = "组合类型")
    private java.lang.String type;
    /**
     * 所属套餐ID
     */
    @Excel(name = "所属套餐ID", width = 15)
    @ApiModelProperty(value = "所属套餐ID")
    private java.lang.String itemSuitId;
    /**
     * 所属套餐名称
     */
    @Excel(name = "所属套餐名称", width = 15)
    @ApiModelProperty(value = "所属套餐名称")
    private java.lang.String itemSuitName;
    /**
     * 加减项标志
     */
    @Excel(name = "加减项标志", width = 15)
    @ApiModelProperty(value = "加减项标志")
    private java.lang.Integer addMinusFlag;
    /**
     * 项目价格
     */
    @Excel(name = "项目价格", width = 15)
    @ApiModelProperty(value = "项目价格")
    private java.math.BigDecimal price;
    /**
     * 折扣率
     */
    @Excel(name = "折扣率", width = 15)
    @ApiModelProperty(value = "折扣率")
    private java.math.BigDecimal disRate;
    /**
     * 折后价
     */
    @Excel(name = "折后价", width = 15)
    @ApiModelProperty(value = "折后价")
    private java.math.BigDecimal priceAfterDis;
    /**
     * 因折扣产生的差额
     */
    @Excel(name = "因折扣产生的差额", width = 15)
    @ApiModelProperty(value = "因折扣产生的差额")
    private BigDecimal priceDisDiffAmount;
    /**
     * 组合的最小折扣率
     */
    @Excel(name = "组合的最小折扣率", width = 15)
    @ApiModelProperty(value = "组合的最小折扣率")
    private BigDecimal minDiscountRate;
    /**
     * 支付方
     */
    @Excel(name = "支付方", width = 15)
    @ApiModelProperty(value = "支付方")
    private java.lang.String payerType;
    /**
     * 支付状态
     */
    @Excel(name = "支付状态", width = 15)
    @ApiModelProperty(value = "支付状态")
    private java.lang.String payStatus;
    /**
     * 检查日期
     */
    @Excel(name = "检查日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "检查日期")
    private java.util.Date checkTime;
    /**r
     * 报告审核日期
     */
    @Excel(name = "报告审核日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "报告审核日期")
    private java.util.Date auditTime;
    /**
     * 检查状态
     */
    @Excel(name = "检查状态", width = 15)
    @ApiModelProperty(value = "检查状态")
    private java.lang.String checkStatus;
    /**
     * 检查状态字典颜色
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "检查状态字典颜色")
    private java.lang.String checkStatusColor;
    /**
     * 登记员工
     */
    @Excel(name = "登记员工", width = 15)
    @ApiModelProperty(value = "登记员工")
    private java.lang.String regBy;
    /**
     * 科室小结状态
     */
    @Excel(name = "科室小结状态", width = 15)
    @ApiModelProperty(value = "科室小结状态")
    private java.lang.String departReportStatus;
    /**
     * 发票信息ID
     */
    @Excel(name = "发票信息ID", width = 15)
    @ApiModelProperty(value = "发票信息ID")
    private java.lang.String receiptId;


    /**
     * 添加时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "添加时间")
    private java.util.Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;

    /**
     * HIS代码
     */
    @Excel(name = "HIS代码", width = 15)
    @ApiModelProperty(value = "HIS代码")
    private java.lang.String hisCode;
    /**
     * HIS名称
     */
    @Excel(name = "HIS名称", width = 15)
    @ApiModelProperty(value = "HIS名称")
    private java.lang.String hisName;
    /**
     * 公卫名称
     */
    @Excel(name = "公卫名称", width = 15)
    @ApiModelProperty(value = "公卫名称")
    private java.lang.String platName;
    /**
     * 公卫代码
     */
    @Excel(name = "公卫代码", width = 15)
    @ApiModelProperty(value = "公卫代码")
    private java.lang.String platCode;

    private String feeRecordId;

    /**
     * 结账单ID
     */
    @ApiModelProperty(value = "结账单ID")
    private String billId;

    private String refundFeeRecordId;

    private String billRefundId;
    /**
     * 支付时间
     */
    @Excel(name = "支付时间", width = 15)
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    /**
     * 项目类别，检查、检验
     */
    @Excel(name = "项目类别", width = 15)
    @ApiModelProperty(value = "项目类别")
    private String classCode;

    /**
     * 检查部位ID
     */
    @Excel(name = "检查部位ID", width = 15)
    @ApiModelProperty(value = "检查部位ID")
    private String checkPartId;

    /**
     * 检查部位名称
     */
    @Excel(name = "检查部位名称", width = 15)
    @ApiModelProperty(value = "检查部位名称")
    private String checkPartName;

    /**
     * 检查部位编码
     */
    @Excel(name = "检查部位编码", width = 15)
    @ApiModelProperty(value = "检查部位编码")
    private String checkPartCode;

    /**
     * 父项目组ID（用于关联同一项目的不同部位）
     */
    @Excel(name = "父项目组ID", width = 15)
    @ApiModelProperty(value = "父项目组ID")
    private String parentGroupId;

    @TableField(exist = false)
    @ApiModelProperty(value = "所在科室")
    private SysDepart department;

    @ApiModelProperty(value = "创建人")
    private String createBy;
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    @ApiModelProperty(value = "创建人")
    private String createName;
    @ApiModelProperty(value = "更新人")
    private String updateName;

    @TableField(exist = false)
    @ApiModelProperty(value = "小项列表")
    private List<ItemInfo> itemList;

    private Integer abandonFlag;

    private String barcodeId;
    @ApiModelProperty(value = "检查结论")
    private String checkConclusion;
    @ApiModelProperty(value = "异常标志")
    private String abnormalFlag;
    @ApiModelProperty(value = "报告PDF")
    private String reportPdf;
    @ApiModelProperty(value = "报告PDF原始路径")
    private String reportPdfInterface;
    @Excel(name = "报告图片", width = 15)
    @ApiModelProperty(value = "报告图片")
    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler.class)
    private List<String> reportPics;
    @TableField(exist = false)
    private List<TextBean> reportPicBeanList;
    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler.class)
    private List<String> reportPicsDicom;
    @Excel(name = "图片", width = 15)
    @ApiModelProperty(value = "图片")
    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler.class)
    private List<String> pic;
    @ApiModelProperty(value = "报告医生代码")
    private String reportDoctorCode;
    @ApiModelProperty(value = "报告医生姓名")
    private String reportDoctorName;
    @ApiModelProperty(value = "报告医生签名")
    private String reportDoctorSignPic;
    @ApiModelProperty(value = "审核医生代码")
    private String auditDoctorCode;
    @ApiModelProperty(value = "审核医生姓名")
    private String auditDoctorName;
    @ApiModelProperty(value = "审核医生签名")
    private String auditDoctorSignPic;
    @ApiModelProperty(value = "检查医生代码")
    private String checkDoctorCode;
    @ApiModelProperty(value = "检查医生姓名")
    private String checkDoctorName;
    @ApiModelProperty(value = "检查医生签名")
    private String checkDoctorSignPic;
    @ApiModelProperty(value = "接口报告时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reportTime;
    @ApiModelProperty(value = "接口报告单号")
    private String checkBillNo;
    @ApiModelProperty(value = "退费锁定")
    private String lockByRefund;
    @ApiModelProperty(value = "必检项目标志")
    private String mustCheckFlag;
    @ApiModelProperty(value = "关联风险因素")
    private String riskFactorId;
    @ApiModelProperty(value = "接口同步状态")
    private String interfaceSyncStatus;
    @ApiModelProperty(value = "赠检标志")
    private String giveAwayFlag;
    @ApiModelProperty("HIS申请单编号")
    private String hisApplyNo;
    @ApiModelProperty("申请单打印次数")
    private Integer applyPrintTimes;

    private String picSyncStatus;

    private String firstCheckAfterPayFlag;

    private String attachBaseId;

    @ApiModelProperty(value = "赠送基础项目ID")
    private String giftBaseId;

    @TableField(exist = false)
    private List<String> attachGroupIds;

    @TableField(exist = false)
    private ItemGroup itemGroup;
    @TableField(exist = false)
    private String departFunction;
    @TableField(exist = false)
    private Integer guideSort;
    @TableField(exist = false)
    private Integer reportSort;
    @TableField(exist = false)
    private String teamId;
    @TableField(exist = false)
    private String companyRegId;
    @TableField(exist = false)
    private String teamName;
    @TableField(exist = false)
    private String companyId;
    @TableField(exist = false)
    private String companyName;
    @TableField(exist = false)
    private String checkDate;
    @TableField(exist = false)
    private String customerId;
    @TableField(exist = false)
    private List<CustomerRegItemResult> resultList;
    @TableField(exist = false)
    private String idCard;
    @TableField(exist = false)
    private String itemNames;
    @TableField(exist = false)
    private String chargeItemOnlyFlag;
    @TableField(exist = false)
    private String name;
    @TableField(exist = false)
    private String changeReason;
    @TableField(exist = false)
    private String changePic;
    @TableField(exist = false)
    private Integer sameGroupQuantity;
    @TableField(exist = false)
    private String groupNameWithQuantity;

    /**
     * 依赖关系列表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "依赖关系列表")
    private List<ItemGroupRelation> dependencies;

    /**
     * 依赖项目结果列表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "依赖项目结果列表")
    private List<DependentItemResultDTO> dependentResults;

    @Override
    public boolean equals(Object o) {

        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CustomerRegItemGroup that = (CustomerRegItemGroup) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

}
