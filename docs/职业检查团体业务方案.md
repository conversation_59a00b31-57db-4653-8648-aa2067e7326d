# 职业检查团体业务解决方案

## 1. 系统现状分析

### 1.1 健康检查 vs 职业检查差异

基于对系统代码的分析，当前系统已具备以下核心功能：

#### 健康检查特点：
- 以个人健康状况评估为主
- 检查项目相对标准化
- 主要关注常见疾病筛查
- 报告侧重个人健康建议

#### 职业检查特点：
- 以职业危害因素为核心
- 检查项目与工作环境相关
- 需要职业病问诊环节
- 报告需符合职业健康监护要求

### 1.2 系统已有职业检查功能

通过代码分析，系统已具备以下职业检查基础功能：

1. **危害因素管理** (`ZyRiskFactor`)
   - 危害因素分类和编码
   - 与工种关联配置
   - 防护措施和建议

2. **职业病问诊** (`ZyInquiry`)
   - 职业史采集
   - 症状问诊
   - 家族史和疾病史
   - 生活习惯调查

3. **团体业务支持** (`CompanyReg`, `CompanyTeam`)
   - 单位预约管理
   - 分组管理
   - 危害因素配置
   - 工种和车间管理

## 2. 职业检查团体业务方案

### 2.1 业务流程设计

#### 2.1.1 团体预约阶段
```
企业联系 → 需求调研 → 危害因素评估 → 检查方案制定 → 合同签订 → 预约确认
```

#### 2.1.2 检查准备阶段
```
人员信息收集 → 分组配置 → 检查项目配置 → 设备准备 → 人员安排
```

#### 2.1.3 现场检查阶段
```
人员签到 → 职业病问诊 → 体格检查 → 辅助检查 → 结果录入 → 质量控制
```

#### 2.1.4 报告生成阶段
```
结果审核 → 职业健康评估 → 个人报告生成 → 团体报告汇总 → 报告发放
```

### 2.2 核心功能模块

#### 2.2.1 企业档案管理
- **企业基本信息**
  - 企业名称、地址、联系方式
  - 行业分类、企业规模
  - 法人代表、安全负责人

- **生产环境信息**
  - 车间布局和工艺流程
  - 危害因素分布图
  - 防护设施配置

#### 2.2.2 危害因素管理
- **危害因素库**
  - 物理因素：噪声、振动、高温、辐射等
  - 化学因素：粉尘、有毒气体、重金属等
  - 生物因素：细菌、病毒、真菌等
  - 其他因素：工效学、心理因素等

- **检查项目关联**
  - 危害因素与检查项目自动匹配
  - 检查频次和周期配置
  - 特殊人群检查要求

#### 2.2.3 分组管理优化
- **智能分组**
  - 按工种自动分组
  - 按危害因素分组
  - 按年龄性别分组
  - 自定义分组规则

- **检查套餐配置**
  - 基础套餐 + 职业特异性项目
  - 不同工种差异化配置
  - 成本控制和预算管理

#### 2.2.4 职业病问诊系统
- **结构化问诊**
  - 职业史采集表单
  - 症状自评量表
  - 既往病史记录
  - 生活习惯调查

- **智能问诊**
  - 根据工种推荐问诊内容
  - 异常症状自动标记
  - 问诊结果风险评估

### 2.3 技术架构设计

#### 2.3.1 数据模型扩展
基于现有数据结构，建议扩展以下表：

1. **企业档案表** (company_profile)
2. **危害因素评估表** (risk_assessment)
3. **检查方案模板表** (exam_template)
4. **职业健康档案表** (occupational_health_record)

#### 2.3.2 系统集成
- **与现有体检系统集成**
  - 复用用户管理、权限控制
  - 复用检查项目、设备管理
  - 复用报告生成、统计分析

- **外部系统对接**
  - 职业病监测系统
  - 企业ERP系统
  - 政府监管平台

## 3. 实施建议

### 3.1 分阶段实施
1. **第一阶段**：完善危害因素管理和问诊系统
2. **第二阶段**：优化团体业务流程和分组管理
3. **第三阶段**：开发智能化功能和数据分析

### 3.2 关键成功因素
- 专业的职业健康团队
- 完善的质量控制体系
- 高效的信息化支撑
- 良好的客户服务体验

## 4. 预期效果

### 4.1 业务效果
- 提升职业检查服务质量
- 扩大团体客户市场份额
- 增强客户粘性和满意度
- 建立行业竞争优势

### 4.2 管理效果
- 规范职业检查流程
- 提高工作效率
- 降低运营成本
- 增强风险控制能力

## 5. 详细功能设计

### 5.1 企业档案管理模块

#### 5.1.1 企业基本信息
- **企业注册信息**
  - 统一社会信用代码
  - 企业名称、地址、联系方式
  - 法人代表、安全负责人
  - 行业分类（按国标分类）
  - 企业规模（人数、产值）

- **生产工艺信息**
  - 主要产品和生产工艺
  - 生产车间布局图
  - 设备清单和工艺流程
  - 原材料使用情况

#### 5.1.2 危害因素档案
- **现场勘察记录**
  - 工作场所平面图
  - 危害因素分布图
  - 检测数据记录
  - 防护设施配置

- **历史检查记录**
  - 历年检查结果汇总
  - 问题整改跟踪
  - 趋势分析报告

### 5.2 智能分组管理

#### 5.2.1 分组策略
```
按工种分组：
- 管理人员组
- 生产操作组
- 维修保养组
- 特殊作业组

按危害因素分组：
- 噪声接触组
- 粉尘接触组
- 化学品接触组
- 辐射接触组

按人群特征分组：
- 新入职人员组
- 在岗人员组
- 离岗人员组
- 特殊人群组（孕期、哺乳期等）
```

#### 5.2.2 检查套餐配置
- **基础套餐**
  - 一般体格检查
  - 基础实验室检查
  - 常规影像检查

- **职业特异性项目**
  - 根据危害因素自动匹配
  - 支持手动调整和补充
  - 成本预算和控制

### 5.3 职业病问诊系统

#### 5.3.1 结构化问诊表单
- **职业史采集**
  - 工作年限和岗位变迁
  - 接触危害因素种类和时间
  - 防护用品使用情况
  - 职业培训和健康教育

- **症状自评**
  - 呼吸系统症状
  - 神经系统症状
  - 皮肤症状
  - 其他相关症状

#### 5.3.2 智能问诊功能
- **问诊内容推荐**
  - 根据工种自动推荐问诊项目
  - 根据危害因素定制问诊内容
  - 支持问诊模板管理

- **异常预警**
  - 症状异常自动标记
  - 风险等级评估
  - 建议进一步检查项目

### 5.4 报告生成系统

#### 5.4.1 个人报告
- **检查结果汇总**
  - 各项检查结果详细列表
  - 异常项目重点标注
  - 历史对比分析

- **职业健康评估**
  - 职业禁忌症筛查
  - 职业相关疾病评估
  - 健康指导建议

#### 5.4.2 团体报告
- **统计分析**
  - 检查人数和完成率
  - 异常检出率统计
  - 危害因素暴露分析

- **趋势分析**
  - 年度对比分析
  - 问题变化趋势
  - 改进建议

## 6. 技术实现要点

### 6.1 数据库设计扩展

基于现有系统，建议新增以下核心表：

```sql
-- 企业档案表
CREATE TABLE company_profile (
    id VARCHAR(32) PRIMARY KEY,
    company_id VARCHAR(32),
    credit_code VARCHAR(50),
    industry_code VARCHAR(20),
    production_process TEXT,
    workshop_layout TEXT,
    create_time DATETIME,
    update_time DATETIME
);

-- 危害因素评估表
CREATE TABLE risk_assessment (
    id VARCHAR(32) PRIMARY KEY,
    company_id VARCHAR(32),
    workshop_id VARCHAR(32),
    risk_factor_id VARCHAR(32),
    exposure_level VARCHAR(20),
    protection_measures TEXT,
    assessment_date DATE
);

-- 检查方案模板表
CREATE TABLE exam_template (
    id VARCHAR(32) PRIMARY KEY,
    template_name VARCHAR(100),
    risk_factors VARCHAR(500),
    exam_items VARCHAR(1000),
    template_type VARCHAR(20),
    is_active TINYINT DEFAULT 1
);
```

### 6.2 接口设计

#### 6.2.1 企业管理接口
```java
@RestController
@RequestMapping("/api/company")
public class CompanyProfileController {

    @PostMapping("/profile")
    public Result saveCompanyProfile(@RequestBody CompanyProfile profile);

    @GetMapping("/risk-assessment/{companyId}")
    public Result getRiskAssessment(@PathVariable String companyId);

    @PostMapping("/exam-template")
    public Result generateExamTemplate(@RequestBody TemplateRequest request);
}
```

#### 6.2.2 问诊管理接口
```java
@RestController
@RequestMapping("/api/inquiry")
public class OccupationalInquiryController {

    @PostMapping("/start")
    public Result startInquiry(@RequestBody InquiryRequest request);

    @PostMapping("/submit")
    public Result submitInquiry(@RequestBody ZyInquiry inquiry);

    @GetMapping("/template/{workType}")
    public Result getInquiryTemplate(@PathVariable String workType);
}
```

### 6.3 前端界面设计

#### 6.3.1 企业档案管理界面
- 企业信息录入和编辑
- 危害因素分布图展示
- 历史检查记录查询
- 检查方案配置

#### 6.3.2 团体检查管理界面
- 预约登记和人员导入
- 智能分组和套餐配置
- 检查进度跟踪
- 质量控制监控

#### 6.3.3 问诊录入界面
- 响应式问诊表单
- 智能提示和验证
- 症状图形化录入
- 历史问诊对比

---

*本方案基于对现有系统的深入分析，结合职业健康监护的法规要求和行业最佳实践制定。方案充分利用现有系统架构，通过功能扩展和优化，实现职业检查团体业务的全面支持。*
